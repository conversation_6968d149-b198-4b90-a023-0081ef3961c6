const { expect, assert } = require('chai');
const request = require('supertest');
const { app } = require('./common');
const sinon = require('sinon');
const User = require('../models/user');
const ScammerCleanup = require('../models/scammer-cleanup');
const constants = require('../lib/constants');

it('scammer cleanup', async () => {

  // create admin users
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  user = await User.findOne({ _id: 1 });
  user.admin = true;
  user.adminPermissions = { support: true };
  await user.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  user = await User.findOne({ _id: 2 });
  user.admin = true;
  user.adminPermissions = { support: true };
  await user.save();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  user = await User.findOne({ _id: 3 });
  user.admin = true;
  user.adminPermissions = { support: true, manager: true };
  await user.save();

  // create other users
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 100);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 101);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 102);
  expect(res.status).to.equal(200);

  // seed data
  await ScammerCleanup.insertMany([
    {
      user: '100',
      verificationPhoto: '100',
      review1: { queue: 1 },
      review2: { queue: 2 },
    },
    {
      user: '101',
      verificationPhoto: '101',
      review1: { queue: 2 },
      review2: { queue: 1 },
    },
    {
      user: '102',
      verificationPhoto: '102',
      review1: { queue: 2 },
      review2: { queue: 3 },
    },
  ]);

  // test getting profiles for review
  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  console.log(res.body.profiles);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles).to.containSubset([{ user: '100', verificationPhoto: '100' }]);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(3);
  expect(res.body.profiles).to.containSubset([{ user: '100', verificationPhoto: '100' }]);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 3 })
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 4 })
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  // incorrect queue number should be rejected
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 3, user: '100', decision: 'ban' })
    .set('authorization', 1);
  expect(res.status).to.equal(422);

  // user 1 decides ban for user 100
  // user 100 removed from queue 1
  // final review queue empty
  // user 100 not banned yet
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 1, user: '100', decision: 'ban' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(3);
  expect(res.body.profiles).to.containSubset([{ user: '100', verificationPhoto: '100' }]);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 100 });
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // user 1 cannot issue a second decision
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 2, user: '100', decision: 'ban' })
    .set('authorization', 1);
  expect(res.status).to.equal(422);

  // another admin cannot send another decision for the same queue number
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 1, user: '100', decision: 'dismiss' })
    .set('authorization', 2);
  expect(res.status).to.equal(422);

  // incorrect queue number should be rejected
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 1, user: '100', decision: 'ban' })
    .set('authorization', 2);
  expect(res.status).to.equal(422);

  // user 2 also decides ban for user 100
  // user 100 removed from queue 2
  // final review queue empty
  // user 100 banned
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 2, user: '100', decision: 'ban' })
    .set('authorization', 2);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(2);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 100 });
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('scammer verification photo cleanup');
  expect(user.bannedNotes).to.equal('also reviewed by 1');
  expect(user.bannedBy).to.equal('2');

  // user 2 decides dismiss for user 101
  // user 101 removed from queue 2
  // final review queue empty
  // user 101 not banned
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 2, user: '101', decision: 'dismiss' })
    .set('authorization', 2);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '101', verificationPhoto: '101' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 101 });
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // user 1 also decides dismiss for user 101
  // user 101 removed from queue 1
  // final review queue empty
  // user 100 not banned
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 1, user: '101', decision: 'dismiss' })
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 101 });
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // user 3 decides dismiss for user 102
  // user 102 removed from queue 3
  // final review queue empty
  // user 100 not banned
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 3, user: '102', decision: 'dismiss' })
    .set('authorization', 3);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles).to.containSubset([{ user: '102', verificationPhoto: '102' }]);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 3 })
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 102 });
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // cannot submit decision for final review yet
  res = await request(app)
    .put('/v1/admin/scammerCleanup/reviewFinal/decision')
    .send({ queue: 3, user: '102', decision: 'dismiss' })
    .set('authorization', 3);
  expect(res.status).to.equal(422);

  // user 2 decides ban for user 102
  // user 102 removed from queue 2
  // user 2 moved to final review queue
  // user 102 not banned yet
  res = await request(app)
    .put('/v1/admin/scammerCleanup/review/decision')
    .send({ queue: 2, user: '102', decision: 'ban' })
    .set('authorization', 2);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 1 })
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 2 })
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/review')
    .query({ queue: 3 })
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(1);
  expect(res.body.profiles[0].user).to.equal('102');
  expect(res.body.profiles[0].verificationPhoto).to.equal('102');

  user = await User.findOne({ _id: 102 });
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // submit ban for final review
  // user 102 removed from final review queue
  // user 102 banned
  res = await request(app)
    .put('/v1/admin/scammerCleanup/reviewFinal/decision')
    .send({ user: '102', decision: 'ban' })
    .set('authorization', 3);
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/admin/scammerCleanup/reviewFinal')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.profiles.length).to.equal(0);

  user = await User.findOne({ _id: 102 });
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('scammer verification photo cleanup');
  expect(user.bannedNotes).to.equal();
  expect(user.bannedBy).to.equal('3');
});

describe('double queue system for photo verification', async () => {
  const challengeId = '1110f6e4-a225-11ed-bf55-d33b0802ecdc';

  beforeEach(async () => {
    // create admin users
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    user = await User.findOne({ _id: 1 });
    user.admin = true;
    user.adminPermissions = { support: true };
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    user = await User.findOne({ _id: 2 });
    user.admin = true;
    user.adminPermissions = { support: true };
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    user = await User.findOne({ _id: 3 });
    user.admin = true;
    user.adminPermissions = { support: true, manager: true };
    await user.save();

    // create user to verify
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.22', locale: 'en' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // start
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            id: challengeId,
            imageWidth: 1000,
            imageHeight: 1000,
            areaLeft: 218,
            areaTop: 125,
            areaWidth: 562,
            areaHeight: 750,
            minFaceAreaPercent: 50,
            noseLeft: 412,
            noseTop: 525,
            noseWidth: 20,
            noseHeight: 20,
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/start')
      .set('authorization', 0)
      .send({ imageWidth: 1000, imageHeight: 1000 });
    expect(res.status).to.equal(200);

    // post frames
    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    // reject
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: false,
            failure_reason: 'Nose not inside nose box',
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Nose challenge failed.');

    // request manual verification
    res = await request(app)
      .post('/v1/liveness/requestManualVerification')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);

    // test getting profiles for review
    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body.users);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    console.log(res.body.users);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);
  });

  it('both verify', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'verify' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // already verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    user = await User.findById('0');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(5);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('verified');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');

    // verifyProfile route should still work
    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 3)
      .send({
        user: '0',
        verified: false,
        rejectionReason: 'Incorrect pose',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Incorrect pose');

    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 3)
      .send({
        user: '0',
        verified: true,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });

  it('both reject', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'reject', rejectionReason: 'Poor lighting.' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // immediately rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Poor lighting.');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'reject', rejectionReason: 'Nose challenge failed.' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Poor lighting.');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    user = await User.findById('0');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(5);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review1.rejectionReason).to.equal('Poor lighting.');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.rejectionReason).to.equal('Poor lighting.');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.review2.rejectionReason).to.equal('Nose challenge failed.');
  });

  it('both ban', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'ban', bannedReason: 'Scammer', bannedNotes: 'scammer' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // immediately rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer (banned during photo verification first review)');
    expect(user.bannedNotes).to.equal('scammer');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'ban', bannedReason: 'Catfish', bannedNotes: 'catfish' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer (banned during photo verification first review)');
    expect(user.bannedNotes).to.equal('scammer');
    expect(user.bannedBy).to.equal('1');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(5);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review1.bannedReason).to.equal('Scammer');
    expect(user.verificationHistory[2].manualReview.review1.bannedNotes).to.equal('scammer');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('auto rejected due to ban');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.bannedReason).to.equal('Scammer');
    expect(user.verificationHistory[4].manualReview.review1.bannedNotes).to.equal('scammer');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('ban');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.review2.bannedReason).to.equal('Catfish');
    expect(user.verificationHistory[4].manualReview.review2.bannedNotes).to.equal('catfish');
  });

  it('first verify second reject - final decision verify', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'verify' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // already verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'reject', rejectionReason: 'Poor lighting.' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');
    console.log(res.body.users[0]);

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'verify' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    user = await User.findById('0');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(6);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('verified');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
  });

  it('first verify second reject - final decision reject', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'verify' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // already verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'reject' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');
    console.log(res.body.users[0]);

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'reject', rejectionReason: 'Nose challenge failed.' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // now rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Nose challenge failed.');

    user = await User.findById('0');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(7);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('verified');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review1.rejectionReason).to.equal();
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.review2.rejectionReason).to.equal();
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
    expect(user.verificationHistory[5].manualReview.reviewFinal.rejectionReason).to.equal('Nose challenge failed.');
    expect(user.verificationHistory[6].status).to.equal('rejected');
    expect(user.verificationHistory[6].reason).to.equal('handled manually by admin');
    expect(user.verificationHistory[6].by).to.equal('3');
  });

  it('first reject second verify - final decision verify', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'reject' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // already rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');
    console.log(res.body.users[0]);

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'verify' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // now verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    user = await User.findById('0');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(7);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
    expect(user.verificationHistory[6].status).to.equal('verified');
    expect(user.verificationHistory[6].reason).to.equal('handled manually by admin');
    expect(user.verificationHistory[6].by).to.equal('3');
  });

  it('first reject then attempt again before second decision', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'reject' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');

    // now attempt again
    const challengeId = 'attempt2';
    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            id: challengeId,
            imageWidth: 1000,
            imageHeight: 1000,
            areaLeft: 218,
            areaTop: 125,
            areaWidth: 562,
            areaHeight: 750,
            minFaceAreaPercent: 50,
            noseLeft: 412,
            noseTop: 525,
            noseWidth: 20,
            noseHeight: 20,
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/start')
      .set('authorization', 0)
      .send({ imageWidth: 1000, imageHeight: 1000 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/liveness/frame')
      .set('authorization', 0)
      .attach('image', validImagePath)
      .query({ challengeId });
    expect(res.status).to.equal(200);

    fakeLambda.invoke = function (params) {
      const impl = function (resolve, reject) {
        resolve({
          Payload: JSON.stringify({
            success: false,
            failure_reason: 'Nose not inside nose box',
          }),
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/liveness/verify')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('rejected');
    expect(res.body.rejectionReason).to.equal('Nose challenge failed.');

    res = await request(app)
      .post('/v1/liveness/requestManualVerification')
      .set('authorization', 0)
      .send({ challengeId });
    expect(res.status).to.equal(200);

    // test getting profiles for review
    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body.users);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    console.log(res.body.users);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    user = await User.findById('0');
    console.log(JSON.stringify(user.verification.manualReview,null,2));
    expect(user.verification.manualReview.review1.reviewedBy).to.equal();
    expect(user.verification.manualReview.review1.reviewedAt).to.equal();
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(6);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('reject');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('handled manually by admin');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].status).to.equal('rejected');
    expect(user.verificationHistory[4].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[5].status).to.equal('pending');
    expect(user.verificationHistory[5].reason).to.equal('user requested manual verification for nose challenge');

    // test submitting a decision
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'reject' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
  });

  it('legacy verification route should cancel double queue system', async () => {
    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 3)
      .send({
        user: '0',
        verified: false,
        rejectionReason: 'Incorrect pose',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Incorrect pose');

    // profile should be removed from double queue system
    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);
  });

  it('first ban second reject - final ban', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'ban', bannedReason: 'Scammer', bannedNotes: 'scammer' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // immediately rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer (banned during photo verification first review)');
    expect(user.bannedNotes).to.equal('scammer');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'reject' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer (banned during photo verification first review)');
    expect(user.bannedNotes).to.equal('scammer');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'ban', bannedReason: 'Catfish', bannedNotes: 'catfish' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer (banned during photo verification first review)');
    expect(user.bannedNotes).to.equal('scammer');
    expect(user.bannedBy).to.equal('1');
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(6);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('auto rejected due to ban');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.bannedReason).to.equal('Scammer');
    expect(user.verificationHistory[4].manualReview.review1.bannedNotes).to.equal('scammer');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.review2.bannedReason).to.equal();
    expect(user.verificationHistory[4].manualReview.review2.bannedNotes).to.equal();
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review1.bannedReason).to.equal('Scammer');
    expect(user.verificationHistory[5].manualReview.review1.bannedNotes).to.equal('scammer');
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.review2.bannedReason).to.equal();
    expect(user.verificationHistory[5].manualReview.review2.bannedNotes).to.equal();
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('ban');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
    expect(user.verificationHistory[5].manualReview.reviewFinal.bannedReason).to.equal('Catfish');
    expect(user.verificationHistory[5].manualReview.reviewFinal.bannedNotes).to.equal('catfish');
  });

  it('first ban second verify - final verify', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'ban', bannedReason: 'Scammer', bannedNotes: 'scammer' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // immediately rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer (banned during photo verification first review)');
    expect(user.bannedNotes).to.equal('scammer');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer (banned during photo verification first review)');
    expect(user.bannedNotes).to.equal('scammer');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'verify' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // now unbanned and verified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();
    expect(user.bannedNotes).to.equal();
    expect(user.bannedBy).to.equal();
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(7);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('auto rejected due to ban');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review1.bannedReason).to.equal('Scammer');
    expect(user.verificationHistory[5].manualReview.review1.bannedNotes).to.equal('scammer');
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.review2.bannedReason).to.equal();
    expect(user.verificationHistory[5].manualReview.review2.bannedNotes).to.equal();
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
    expect(user.verificationHistory[5].manualReview.reviewFinal.bannedReason).to.equal();
    expect(user.verificationHistory[5].manualReview.reviewFinal.bannedNotes).to.equal();
    expect(user.verificationHistory[6].status).to.equal('verified');
    expect(user.verificationHistory[6].reason).to.equal('handled manually by admin');
    expect(user.verificationHistory[6].by).to.equal('3');
  });

  it('first ban second verify - final reject', async () => {
    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 1, user: '0', decision: 'ban', bannedReason: 'Scammer', bannedNotes: 'scammer' })
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 1 })
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // immediately rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer (banned during photo verification first review)');
    expect(user.bannedNotes).to.equal('scammer');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/review/decision')
      .send({ queue: 2, user: '0', decision: 'verify' })
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/review')
      .query({ queue: 2 })
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // still rejected and banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer (banned during photo verification first review)');
    expect(user.bannedNotes).to.equal('scammer');
    expect(user.bannedBy).to.equal('1');

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('0');

    res = await request(app)
      .put('/v1/admin/verifyProfileNew/reviewFinal/decision')
      .send({ user: '0', decision: 'reject' })
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfileNew/reviewFinal')
      .set('authorization', 3);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    // now unbanned but still unverified
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal();

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();
    expect(user.bannedNotes).to.equal();
    expect(user.bannedBy).to.equal();
    console.log(JSON.stringify(user.verificationHistory,null,2));
    expect(user.verificationHistory.length).to.equal(6);
    expect(user.verificationHistory[0].status).to.equal('rejected');
    expect(user.verificationHistory[0].reason).to.equal('failed nose challenge');
    expect(user.verificationHistory[1].status).to.equal('pending');
    expect(user.verificationHistory[1].reason).to.equal('user requested manual verification for nose challenge');
    expect(user.verificationHistory[2].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[2].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[2].manualReview.review2.decision).to.equal(null);
    expect(user.verificationHistory[3].status).to.equal('rejected');
    expect(user.verificationHistory[3].reason).to.equal('auto rejected due to ban');
    expect(user.verificationHistory[3].by).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[4].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[4].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[4].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[4].manualReview.reviewFinal.decision).to.equal(null);
    expect(user.verificationHistory[5].manualReview.review1.decision).to.equal('ban');
    expect(user.verificationHistory[5].manualReview.review1.reviewedBy).to.equal('1');
    expect(user.verificationHistory[5].manualReview.review2.decision).to.equal('verify');
    expect(user.verificationHistory[5].manualReview.review2.reviewedBy).to.equal('2');
    expect(user.verificationHistory[5].manualReview.reviewFinal.decision).to.equal('reject');
    expect(user.verificationHistory[5].manualReview.reviewFinal.reviewedBy).to.equal('3');
  });
});
